import { GenericRepository } from '@app/shared-stuff';
import { Injectable } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JobLog, JobLogDocument } from '../../models/job-log.model';
import { JobLogRepositoryInterface } from './job-log.repository.interface';

@Injectable()
export class JobLogRepository
  extends GenericRepository<JobLogDocument, JobLog>
  implements JobLogRepositoryInterface
{
  constructor(
    @InjectModel(JobLog.name)
    private JobLogModel: Model<JobLogDocument, JobLog>,
  ) {
    super(JobLogModel);
  }
}
